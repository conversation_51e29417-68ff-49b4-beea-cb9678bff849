#import './FileAttachmentMainFragment.graphql'

query programSubjectsTree(
  $programGroupId: Int!
  $searchQuery: String
  $status: [Status!]! = [ACTIVE]
) {
  programSubjectsTree(
    programGroupId: $programGroupId
    searchQuery: $searchQuery
    status: $status
  ) {
    __typename
    id
    name
    code
    status
    parentId
    programGroupId
    orgGroupId
    sequence

    ... on ProgramSubject {
      markBook
      gradeScaleId
      attendanceCodeSet
      attendanceCodeSetId
      submissions
      learningPlan
      colour
      isScheduled
      mobileAppIcon
      subjectAttachments {
        ...FileAttachmentMain
      }

      sessionHoursCredits
      timetable

      programGroupSessions {
        id
        creditRatio
        minutes
        per
        programGroupSessionId
        credits
      }
      timetables {
        id
        periodNum
        periodType
      }
    }
  }
}
