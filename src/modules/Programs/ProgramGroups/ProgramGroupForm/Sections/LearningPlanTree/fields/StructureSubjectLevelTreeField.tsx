import React, { FC, useCallback, useMemo, useState } from 'react';
import { map, some, startsWith } from 'lodash';

import TreeSelectorField from '../../../../../../../common/components/containers/EntityForm/fields/TreeSelectorField';
import nodeSortingOptionsSequence from '../../../../../../../common/utils/nodeSortingOptionsSequence';
import { SelectionMode } from '../../../../../../../common/components/dataViews/DynamicTree/constants';
import learningPlanTreeWithoutLP from '../data/learningPlanTreeWithoutLP.graphql';
import useLabel from '../../../../../../../common/components/utils/Translations/useLabel';
import {
  isProgram,
  isProgramFolder,
} from '../../../../../../../model/ProgramGroupProgramTypeNames';
import { Active } from '../../../../../../../model/Statuses';
import { catsearch } from '../../../../../../../common/textSearch';
import FilterBar from '../../../../../../../common/components/controls/FilterBar';
import { IProgram } from '../../../../../../../model/ProgramGroupStructureTypeNames';
import ProgramTreeSelector from '../../../../../../../common/components/controls/FilterBar/ProgramTreeSelector';
import SearchField from '../../../../../../../common/components/controls/base/SearchField';
import useT from '../../../../../../../common/components/utils/Translations/useT';

export interface IStructureSubjectLevelTreeField {
  programGroupId: number;
  title?: string;
  name?: string;
  label?: string;
  isRequired?: boolean;
  isDisabled?: boolean;
  rootName: string;
  syntheticRootNodeName: string;
  excludeId?: number;
  programName: number;
  programs?: IProgram[];
}
const StructureSubjectLevelTreeField: FC<IStructureSubjectLevelTreeField> = ({
  name = 'parentLevel',
  label,
  isRequired = true,
  isDisabled,
  programGroupId,
  rootName,
  syntheticRootNodeName,
  excludeId,
  programName,
  programs,
  ...props
}) => {
  const t = useT();
  const _label = useLabel('Parent Level', label);

  const nodeIsVisible = useCallback(node => node.id !== excludeId, [excludeId]);

  const [filters, setFilters] = useState({
    status: Active.value,
    searchQuery: '',
    programs: programs?.filter(x => x.status === Active.value),
  });
  const _handleChangeSearch = useCallback(
    searchValue => {
      setFilters({
        ...filters,
        searchQuery: searchValue,
      });
    },
    [filters, setFilters],
  );

  const updateFilters = useCallback(
    ({ programs }) => {
      setFilters({
        ...filters,
        programs,
      });
    },
    [filters, setFilters],
  );

  const makeStatusMatcher = useCallback(
    () => ({ status }) => [Active.value].includes(status),
    [],
  );

  const makeProgramMatcher = useCallback(
    value => ({ programId }) => map(value, 'id').includes(programId),
    [],
  );

  const predicatesMeta = useMemo(
    () => [
      {
        valuePropName: 'status',
        predicate: makeStatusMatcher,
        includeChildren: false,
        collectVisibleIds: true,
      },
      {
        valuePropName: 'searchQuery',
        predicate: value => node => catsearch(node.name, value),
        includeChildren: false,
        collectVisibleIds: true,
      },
      {
        valuePropName: 'programs',
        predicate: makeProgramMatcher,
        includeChildren: false,
        collectVisibleIds: true,
      },
    ],
    [makeStatusMatcher, makeProgramMatcher],
  );

  const programTreeSelector = useCallback(
    props => (
      <>
        <FilterBar
          searchName="searchQuery"
          {...props}
          values={{
            ...props.value,
          }}
        >
          <ProgramTreeSelector
            hasFilter
            hasSearchFieldOnly={false}
            name="programs"
            programGroupId={programGroupId}
            programs={programs}
            syntheticRootNodeName={syntheticRootNodeName}
            title={programName}
          />
        </FilterBar>
        <SearchField
          placeholder={t('Search')}
          value={filters.searchQuery}
          onChange={_handleChangeSearch}
        />
      </>
    ),
    [
      programGroupId,
      programs,
      programName,
      syntheticRootNodeName,
      _handleChangeSearch,
      t,
      filters,
    ],
  );

  return (
    <TreeSelectorField
      hasSyntheticRootNode
      adapter={{
        resolveNodeId,
        resolveNodeParentId,
        canRender,
      }}
      columns={3}
      disabled={isDisabled}
      filter={{
        component: programTreeSelector,
        predicatesMeta,
        value: filters,
        onChange: updateFilters,
      }}
      gql={learningPlanTreeWithoutLP}
      gqlFetchPolicy="cache-and-network"
      gqlSkip={!programGroupId}
      gqlVariables={{
        programGroupId,
      }}
      hasSpinner={false}
      label={_label}
      name={name}
      plugins={{
        nodeIsSelectable,
        nodeIsVisible,
        nodeSortingOptions: nodeSortingOptionsSequence,
      }}
      required={isRequired}
      selectionMode={SelectionMode.SINGLE}
      syntheticRootNodeName={rootName}
      tooltipPosition="responsive"
      {...props}
    />
  );
};

const ProgramFolder = 'ProgramFolder';
const Program = 'Program';
const ProgramStructureSubject = 'ProgramStructureSubject';
const LearningPlan = 'LearningPlan';

export const isProgramStructureSubject = ({ __typename }) =>
  __typename === ProgramStructureSubject;
const isLearningPlan = ({ __typename }) => __typename === LearningPlan;

const resolveNodeId = node => {
  if (isProgramFolder(node)) {
    return `${ProgramFolder}-${node.id}`;
  }
  if (isProgram(node)) {
    return `${Program}-${node.id}`;
  }
  if (isProgramStructureSubject(node)) {
    return `${ProgramStructureSubject}-${node.id}`;
  }
  if (isLearningPlan(node)) {
    return `${LearningPlan}-${node.id}`;
  }

  return node.id;
};

const resolveNodeParentId = node => {
  if (isProgramFolder(node) && node.parentId) {
    return `${ProgramFolder}-${node.parentId}`;
  }
  if (isProgram(node) && node.programFolderId) {
    return `${ProgramFolder}-${node.programFolderId}`;
  }
  if (isProgramStructureSubject(node) && node.programId) {
    return `${Program}-${node.programId}`;
  }
  if (isLearningPlan(node)) {
    return `${ProgramStructureSubject}-${node.subjectId}`;
  }

  return node.parentId;
};

const canRender = ({ id, model, leaf, nodesMeta: { hasChild }, state }) => {
  const checkLeafExistsRecursively = (nodeId: string): boolean => {
    if (!state.children[nodeId] || !state.children[nodeId].length) {
      return false;
    }

    const leafExists = some(state.children[nodeId], str =>
      startsWith(str, `${ProgramStructureSubject}-`),
    );

    if (leafExists) {
      return true;
    }

    return some(state.children[nodeId], childId =>
      checkLeafExistsRecursively(childId),
    );
  };

  if (
    (model.id !== ':ROOT:' || model.id !== null) &&
    !isProgramStructureSubject(model)
  ) {
    if (state.children[id]) {
      const leafExists = checkLeafExistsRecursively(id);

      if (!leafExists || leaf) {
        return false;
      }
    } else {
      return false;
    }
  }
  return true;
};

const nodeIsSelectable = node => isProgramStructureSubject(node);

export default StructureSubjectLevelTreeField;
